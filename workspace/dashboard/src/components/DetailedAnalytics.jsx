import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { marketTrends, competitiveAnalysis } from '../data/realEstateData';

const DetailedAnalytics = () => {
  const occupancyData = Object.entries(marketTrends.occupancyRates).map(([type, rate]) => ({
    type: type.charAt(0).toUpperCase() + type.slice(1),
    occupancy: rate,
    vacancy: 100 - rate
  }));

  const priceRangeData = Object.entries(marketTrends.priceRanges).map(([type, data]) => ({
    type: type.charAt(0).toUpperCase() + type.slice(1),
    min: data.min,
    avg: data.avg,
    max: data.max
  }));

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Quarterly Growth Trends */}
      <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-800">Quarterly Growth Trends</h3>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600 font-medium">YoY Growth</span>
          </div>
        </div>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={marketTrends.quarterlyGrowth}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="quarter" />
            <YAxis yAxisId="left" orientation="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip 
              formatter={(value, name) => {
                if (name === 'growth') return [`${value}%`, 'Growth Rate'];
                return [`$${value}`, 'Avg Rent'];
              }}
            />
            <Legend />
            <Bar yAxisId="left" dataKey="growth" fill="#8884d8" name="Growth Rate (%)" />
            <Line yAxisId="right" type="monotone" dataKey="avgRent" stroke="#82ca9d" strokeWidth={3} name="Avg Rent ($)" />
          </LineChart>
        </ResponsiveContainer>
        <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
          <p className="text-sm text-gray-700">
            <span className="font-semibold">Insight:</span> Q4 2024 shows the strongest growth at 3.5%, 
            with average rents reaching $3.46/sqft, indicating a robust market recovery.
          </p>
        </div>
      </div>

      {/* Occupancy vs Vacancy Rates */}
      <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-800">Occupancy Rates by Type</h3>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600 font-medium">Current Status</span>
          </div>
        </div>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={occupancyData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="type" />
            <YAxis />
            <Tooltip formatter={(value) => [`${value}%`, '']} />
            <Legend />
            <Bar dataKey="occupancy" stackId="a" fill="#4ade80" name="Occupied %" />
            <Bar dataKey="vacancy" stackId="a" fill="#f87171" name="Vacant %" />
          </BarChart>
        </ResponsiveContainer>
        <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
          <p className="text-sm text-gray-700">
            <span className="font-semibold">Insight:</span> Warehouse properties show the highest occupancy at 94.5%, 
            while office spaces have the most room for improvement at 82.3%.
          </p>
        </div>
      </div>

      {/* Price Range Analysis */}
      <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-800">Price Range Analysis</h3>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600 font-medium">Per SqFt ($)</span>
          </div>
        </div>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={priceRangeData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="type" />
            <YAxis />
            <Tooltip formatter={(value) => [`$${value}`, '']} />
            <Legend />
            <Bar dataKey="min" fill="#93c5fd" name="Min Price" />
            <Bar dataKey="avg" fill="#3b82f6" name="Avg Price" />
            <Bar dataKey="max" fill="#1d4ed8" name="Max Price" />
          </BarChart>
        </ResponsiveContainer>
        <div className="mt-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
          <p className="text-sm text-gray-700">
            <span className="font-semibold">Insight:</span> Restaurant properties command premium rents with 
            an average of $3.75/sqft, while warehouse spaces offer the most affordable options at $2.15/sqft.
          </p>
        </div>
      </div>

      {/* Competitive Market Share */}
      <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-800">Market Share Analysis</h3>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-600 font-medium">Top Competitors</span>
          </div>
        </div>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={competitiveAnalysis}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(1)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="marketShare"
            >
              {competitiveAnalysis.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value) => [`${value}%`, 'Market Share']} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
        <div className="mt-4 p-4 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl">
          <p className="text-sm text-gray-700">
            <span className="font-semibold">Insight:</span> Downtown Properties leads with 23.5% market share, 
            while the market remains competitive with 30.1% held by smaller players.
          </p>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="lg:col-span-2 glass p-8 rounded-2xl shadow-xl border border-white/20">
        <h3 className="text-2xl font-bold text-gray-800 mb-6">Key Performance Indicators</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-800 mb-1">Market Growth</h4>
            <p className="text-3xl font-bold text-blue-600">+3.2%</p>
            <p className="text-sm text-gray-600">Quarterly Average</p>
          </div>

          <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl">
            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-800 mb-1">Avg Occupancy</h4>
            <p className="text-3xl font-bold text-green-600">88.3%</p>
            <p className="text-sm text-gray-600">Across All Types</p>
          </div>

          <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
            <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-800 mb-1">Premium Rate</h4>
            <p className="text-3xl font-bold text-purple-600">$3.46</p>
            <p className="text-sm text-gray-600">Average per SqFt</p>
          </div>

          <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl">
            <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-gray-800 mb-1">Market Velocity</h4>
            <p className="text-3xl font-bold text-orange-600">42 days</p>
            <p className="text-sm text-gray-600">Avg Time to Lease</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailedAnalytics;
