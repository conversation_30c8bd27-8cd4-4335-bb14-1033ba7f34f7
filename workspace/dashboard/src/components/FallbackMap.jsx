import React, { useState } from 'react';
import { propertyListings } from '../data/realEstateData';

const FallbackMap = ({ selectedProperty, onPropertySelect }) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Simple search functionality for the fallback
  const filteredProperties = propertyListings.filter(property => 
    !searchTerm || 
    property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
    property.propertyType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="w-full h-64 rounded-lg border-2 border-gray-200 bg-gradient-to-br from-blue-50 to-indigo-100" style={{ minHeight: '400px' }}>
      {/* Search Box */}
      <div className="absolute top-4 left-4 z-10 w-80">
        <div className="relative">
          <input
            type="text"
            placeholder="Search properties by address or type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-3 pr-12 bg-white border border-gray-300 rounded-lg shadow-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
        
        {searchTerm && (
          <button
            onClick={() => setSearchTerm('')}
            className="absolute top-full mt-2 px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs rounded transition-colors"
          >
            Clear Search
          </button>
        )}
      </div>

      {/* Fallback Map Content */}
      <div className="relative w-full h-full flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">Property Locations</h3>
          <p className="text-gray-600 mb-6 max-w-md">
            Interactive map is temporarily unavailable. Browse properties below or use the search to find specific locations.
          </p>
          
          {/* Property Quick List */}
          <div className="bg-white rounded-lg shadow-sm p-4 max-w-md mx-auto">
            <h4 className="font-medium text-gray-800 mb-3">
              {filteredProperties.length} Properties Available
            </h4>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {filteredProperties.slice(0, 5).map((property) => (
                <button
                  key={property.id}
                  onClick={() => onPropertySelect && onPropertySelect(property)}
                  className="w-full text-left p-2 hover:bg-gray-50 rounded text-sm border border-gray-100 transition-colors"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-gray-800">{property.address}</p>
                      <p className="text-gray-600">{property.propertyType}</p>
                    </div>
                    <p className="text-green-600 font-semibold text-xs">
                      ${property.monthlyRent.toLocaleString()}/mo
                    </p>
                  </div>
                </button>
              ))}
              {filteredProperties.length > 5 && (
                <p className="text-xs text-gray-500 text-center pt-2">
                  +{filteredProperties.length - 5} more properties available below
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-4 right-4 opacity-20">
          <svg className="w-12 h-12 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
          </svg>
        </div>
        <div className="absolute bottom-4 left-4 opacity-20">
          <svg className="w-8 h-8 text-indigo-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
          </svg>
        </div>
        <div className="absolute top-1/2 right-8 opacity-20">
          <svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default FallbackMap;
