// Utility functions for location-based operations

/**
 * Calculate distance between two coordinates using Haversine formula
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} Distance in miles
 */
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 3959; // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

/**
 * Find properties within a certain radius of a location
 * @param {Array} properties - Array of property objects with latitude/longitude
 * @param {number} centerLat - Center latitude
 * @param {number} centerLng - Center longitude
 * @param {number} radiusMiles - Search radius in miles (default: 2)
 * @returns {Array} Properties within radius, sorted by distance
 */
export const findNearbyProperties = (properties, centerLat, centerLng, radiusMiles = 2) => {
  return properties
    .map(property => ({
      ...property,
      distance: calculateDistance(centerLat, centerLng, property.latitude, property.longitude)
    }))
    .filter(property => property.distance <= radiusMiles)
    .sort((a, b) => a.distance - b.distance);
};

/**
 * Get formatted address from Google Places result
 * @param {Object} place - Google Places API result
 * @returns {string} Formatted address
 */
export const getFormattedAddress = (place) => {
  if (place.formatted_address) {
    return place.formatted_address;
  }
  
  // Fallback to constructing address from components
  const components = place.address_components || [];
  const streetNumber = components.find(c => c.types.includes('street_number'))?.long_name || '';
  const streetName = components.find(c => c.types.includes('route'))?.long_name || '';
  const city = components.find(c => c.types.includes('locality'))?.long_name || '';
  const state = components.find(c => c.types.includes('administrative_area_level_1'))?.short_name || '';
  
  return `${streetNumber} ${streetName}, ${city}, ${state}`.trim();
};

/**
 * Geocode an address using Google Maps Geocoding API
 * @param {string} address - Address to geocode
 * @param {string} apiKey - Google Maps API key
 * @returns {Promise<Object>} Geocoding result with lat/lng
 */
export const geocodeAddress = async (address, apiKey) => {
  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${apiKey}`
    );
    const data = await response.json();
    
    if (data.status === 'OK' && data.results.length > 0) {
      const result = data.results[0];
      return {
        lat: result.geometry.location.lat,
        lng: result.geometry.location.lng,
        formatted_address: result.formatted_address,
        place_id: result.place_id
      };
    }
    
    throw new Error(`Geocoding failed: ${data.status}`);
  } catch (error) {
    console.error('Geocoding error:', error);
    throw error;
  }
};

/**
 * Get current user location
 * @returns {Promise<Object>} User's current coordinates
 */
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy
        });
      },
      (error) => {
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  });
};
