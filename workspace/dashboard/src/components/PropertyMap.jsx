import React, { useState } from 'react';
import GoogleMap from './GoogleMap';
import { propertyListings } from '../data/realEstateData';

const PropertyMap = () => {
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [nearbyProperties, setNearbyProperties] = useState([]);
  const [searchLocation, setSearchLocation] = useState(null);
  const [showNearbyOnly, setShowNearbyOnly] = useState(false);
  const [filters, setFilters] = useState({
    propertyType: 'all',
    maxRent: 20000,
    minSqft: 0
  });

  // Handle nearby properties found from map search
  const handleNearbyPropertiesFound = (properties, location) => {
    setNearbyProperties(properties);
    setSearchLocation(location);
    if (properties.length > 0) {
      setShowNearbyOnly(true);
    }
  };

  // Determine which properties to show
  const getPropertiesToShow = () => {
    let baseProperties = showNearbyOnly && nearbyProperties.length > 0
      ? nearbyProperties
      : propertyListings;

    return baseProperties.filter(property => {
      const matchesSearch = !searchTerm ||
        property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.propertyType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.agent.name.toLowerCase().includes(searchTerm.toLowerCase());

      return (
        matchesSearch &&
        (filters.propertyType === 'all' || property.propertyType.toLowerCase() === filters.propertyType) &&
        property.monthlyRent <= filters.maxRent &&
        property.sqft >= filters.minSqft
      );
    });
  };

  const filteredProperties = getPropertiesToShow();

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-lg font-medium text-gray-800">Property Map & Listings</h2>
            {showNearbyOnly && searchLocation && (
              <p className="text-sm text-blue-600 mt-1">
                Showing properties near {searchLocation.name}
              </p>
            )}
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-600">
              {filteredProperties.length} properties found
            </div>
            {showNearbyOnly && (
              <button
                onClick={() => {
                  setShowNearbyOnly(false);
                  setNearbyProperties([]);
                  setSearchLocation(null);
                }}
                className="text-xs text-blue-600 hover:text-blue-800 underline mt-1"
              >
                Show all properties
              </button>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search properties by address, type, or agent..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          <select
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={filters.propertyType}
            onChange={(e) => setFilters({...filters, propertyType: e.target.value})}
          >
            <option value="all">All Types</option>
            <option value="retail">Retail</option>
            <option value="office">Office</option>
            <option value="restaurant">Restaurant</option>
          </select>

          <input
            type="number"
            placeholder="Max Rent"
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={filters.maxRent}
            onChange={(e) => setFilters({...filters, maxRent: parseInt(e.target.value) || 20000})}
          />
        </div>

        {/* Clear filters button */}
        {(searchTerm || filters.propertyType !== 'all' || filters.maxRent !== 20000 || showNearbyOnly) && (
          <div className="mt-3 flex space-x-4">
            <button
              onClick={() => {
                setSearchTerm('');
                setFilters({
                  propertyType: 'all',
                  maxRent: 20000,
                  minSqft: 0
                });
              }}
              className="text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Clear filters
            </button>
            {showNearbyOnly && (
              <button
                onClick={() => {
                  setShowNearbyOnly(false);
                  setNearbyProperties([]);
                  setSearchLocation(null);
                }}
                className="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Show all properties
              </button>
            )}
          </div>
        )}
      </div>
      
      {/* Google Maps Integration */}
      <GoogleMap
        selectedProperty={selectedProperty}
        onPropertySelect={setSelectedProperty}
        onNearbyPropertiesFound={handleNearbyPropertiesFound}
      />

      {/* Property Grid */}
      {filteredProperties.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredProperties.map((property) => (
            <div
              key={property.id}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => setSelectedProperty(property)}
            >
              <div className="flex justify-between items-start mb-2">
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                  {property.propertyType}
                </span>
                <span className="text-green-600 font-semibold">${property.monthlyRent.toLocaleString()}/mo</span>
              </div>

              <h3 className="font-medium text-gray-800 mb-1">{property.address}</h3>
              <p className="text-sm text-gray-600 mb-2">
                {property.sqft.toLocaleString()} sqft • ${property.pricePerSqft}/sqft
                {property.distance && (
                  <span className="ml-2 text-blue-600">• {property.distance.toFixed(1)} mi away</span>
                )}
              </p>

              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Agent: {property.agent.name}</span>
                <button className="text-blue-600 hover:text-blue-800">Contact</button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">No Properties Found</h3>
          <p className="text-gray-600 mb-4">
            {showNearbyOnly && searchLocation
              ? `No properties found near ${searchLocation.name} with your current filters`
              : searchTerm
                ? `No properties match "${searchTerm}"`
                : 'No properties match your current filters'
            }
          </p>
          <div className="space-x-4">
            <button
              onClick={() => {
                setSearchTerm('');
                setFilters({
                  propertyType: 'all',
                  maxRent: 20000,
                  minSqft: 0
                });
              }}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              Clear filters
            </button>
            {showNearbyOnly && (
              <button
                onClick={() => {
                  setShowNearbyOnly(false);
                  setNearbyProperties([]);
                  setSearchLocation(null);
                }}
                className="text-blue-600 hover:text-blue-800 underline"
              >
                Show all properties
              </button>
            )}
          </div>
        </div>
      )}

      {/* Property Detail Modal */}
      {selectedProperty && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold">{selectedProperty.address}</h3>
              <button 
                onClick={() => setSelectedProperty(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Type:</span>
                <span>{selectedProperty.propertyType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Size:</span>
                <span>{selectedProperty.sqft.toLocaleString()} sqft</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Monthly Rent:</span>
                <span className="text-green-600 font-semibold">${selectedProperty.monthlyRent.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Price/sqft:</span>
                <span>${selectedProperty.pricePerSqft}</span>
              </div>
              
              <div className="pt-2">
                <p className="text-sm text-gray-600 mb-2">Amenities:</p>
                <div className="flex flex-wrap gap-1">
                  {selectedProperty.amenities.map((amenity, index) => (
                    <span key={index} className="bg-gray-100 text-xs px-2 py-1 rounded">
                      {amenity}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="pt-2">
                <p className="text-sm text-gray-700">{selectedProperty.description}</p>
              </div>
              
              <div className="pt-4 border-t">
                <p className="text-sm font-medium">Contact Agent:</p>
                <p className="text-sm">{selectedProperty.agent.name}</p>
                <p className="text-sm text-blue-600">{selectedProperty.agent.phone}</p>
                <p className="text-sm text-blue-600">{selectedProperty.agent.email}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PropertyMap;