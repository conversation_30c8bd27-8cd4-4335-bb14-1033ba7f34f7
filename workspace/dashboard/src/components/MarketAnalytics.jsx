import React, { useState } from 'react';
import StatsCard from './StatsCard';
import PropertyTypeChart from './charts/PropertyTypeChart';
import RentTrendsChart from './charts/RentTrendsChart';
import ZipCodeAnalyticsChart from './charts/ZipCodeAnalyticsChart';
import FootTraffic<PERSON>hart from './charts/FootTrafficChart';
import DetailedAnalytics from './DetailedAnalytics';
import {
  marketStats,
  propertyListings,
  zipCodeAnalytics,
  propertyTypeData,
  monthlyRentTrends
} from '../data/realEstateData';

const MarketAnalytics = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('6months');
  const [selectedPropertyType, setSelectedPropertyType] = useState('all');
  const [selectedZipCode, setSelectedZipCode] = useState('all');

  // Calculate dynamic metrics based on filters
  const getFilteredProperties = () => {
    let filtered = propertyListings;
    
    if (selectedPropertyType !== 'all') {
      filtered = filtered.filter(prop => prop.propertyType.toLowerCase() === selectedPropertyType);
    }
    
    return filtered;
  };

  const filteredProperties = getFilteredProperties();
  const availableProperties = filteredProperties.filter(prop => prop.status === 'Available');
  const avgRent = filteredProperties.reduce((sum, prop) => sum + prop.pricePerSqft, 0) / filteredProperties.length;
  const vacancyRate = ((filteredProperties.length - availableProperties.length) / filteredProperties.length * 100);

  // Enhanced market stats with real calculations
  const enhancedMarketStats = [
    {
      ...marketStats[0],
      value: availableProperties.length.toString(),
      change: '+8.2%',
      trend: 'up'
    },
    {
      ...marketStats[1],
      value: `$${avgRent.toFixed(2)}`,
      change: '+2.1%',
      trend: 'up'
    },
    {
      ...marketStats[2],
      value: `${vacancyRate.toFixed(1)}%`,
      change: '-1.5%',
      trend: 'down'
    },
    {
      ...marketStats[3],
      value: '23',
      change: '+15.3%',
      trend: 'up'
    }
  ];

  const timeframeOptions = [
    { value: '1month', label: '1 Month' },
    { value: '3months', label: '3 Months' },
    { value: '6months', label: '6 Months' },
    { value: '1year', label: '1 Year' },
    { value: '2years', label: '2 Years' }
  ];

  const propertyTypeOptions = [
    { value: 'all', label: 'All Types' },
    { value: 'retail', label: 'Retail' },
    { value: 'office', label: 'Office' },
    { value: 'restaurant', label: 'Restaurant' },
    { value: 'warehouse', label: 'Warehouse' },
    { value: 'mixed use', label: 'Mixed Use' }
  ];

  const zipCodeOptions = [
    { value: 'all', label: 'All Zip Codes' },
    ...zipCodeAnalytics.map(zip => ({ value: zip.zipCode, label: zip.zipCode }))
  ];

  return (
    <div className="space-y-8 animate-slide-in">
      {/* Header Section */}
      <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className="mb-6 lg:mb-0">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
              Market Analytics
            </h1>
            <p className="text-lg text-gray-600 font-medium">
              Comprehensive commercial real estate market insights and trends
            </p>
          </div>
          
          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex flex-col">
              <label className="text-sm font-semibold text-gray-700 mb-2">Timeframe</label>
              <select 
                value={selectedTimeframe}
                onChange={(e) => setSelectedTimeframe(e.target.value)}
                className="px-4 py-2 bg-white/80 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              >
                {timeframeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            <div className="flex flex-col">
              <label className="text-sm font-semibold text-gray-700 mb-2">Property Type</label>
              <select 
                value={selectedPropertyType}
                onChange={(e) => setSelectedPropertyType(e.target.value)}
                className="px-4 py-2 bg-white/80 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              >
                {propertyTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            <div className="flex flex-col">
              <label className="text-sm font-semibold text-gray-700 mb-2">Zip Code</label>
              <select 
                value={selectedZipCode}
                onChange={(e) => setSelectedZipCode(e.target.value)}
                className="px-4 py-2 bg-white/80 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              >
                {zipCodeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Market Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {enhancedMarketStats.map((stat, index) => (
          <StatsCard 
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            trend={stat.trend}
            icon={stat.icon}
          />
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Property Type Distribution */}
        <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-800">Property Type Distribution</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600 font-medium">Live Data</span>
            </div>
          </div>
          <PropertyTypeChart />
          <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
            <p className="text-sm text-gray-700">
              <span className="font-semibold">Insight:</span> Retail properties dominate the market at 35%, 
              followed by office spaces at 27%. Mixed-use developments are growing rapidly.
            </p>
          </div>
        </div>

        {/* Rent Trends */}
        <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-800">Monthly Rent Trends</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600 font-medium">6 Month Trend</span>
            </div>
          </div>
          <RentTrendsChart />
          <div className="mt-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
            <p className="text-sm text-gray-700">
              <span className="font-semibold">Insight:</span> Restaurant spaces command the highest rents at $3.80/sqft, 
              with consistent 2-3% monthly growth across all property types.
            </p>
          </div>
        </div>
      </div>

      {/* Additional Analytics Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Zip Code Analytics */}
        <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-800">Zip Code Performance</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600 font-medium">Market Analysis</span>
            </div>
          </div>
          <ZipCodeAnalyticsChart />
          <div className="mt-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl">
            <p className="text-sm text-gray-700">
              <span className="font-semibold">Insight:</span> Zip code 10004 shows the highest rent at $4.00/sqft 
              with the lowest vacancy rate at 5.8%, indicating strong market demand.
            </p>
          </div>
        </div>

        {/* Foot Traffic Analysis */}
        <div className="glass p-8 rounded-2xl shadow-xl border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-800">Foot Traffic Patterns</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600 font-medium">Daily Average</span>
            </div>
          </div>
          <FootTrafficChart />
          <div className="mt-4 p-4 bg-gradient-to-r from-orange-50 to-yellow-50 rounded-xl">
            <p className="text-sm text-gray-700">
              <span className="font-semibold">Insight:</span> Peak foot traffic occurs at 6PM on weekdays (400 people/hour) 
              and 9PM on weekends (420 people/hour), ideal for retail businesses.
            </p>
          </div>
        </div>
      </div>

      {/* Advanced Analytics Section */}
      <div className="space-y-6">
        <div className="glass p-6 rounded-2xl shadow-xl border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
              Advanced Market Intelligence
            </h2>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600 font-medium">Deep Analytics</span>
            </div>
          </div>
          <p className="text-gray-600 font-medium mb-4">
            Comprehensive market analysis with competitive intelligence and growth projections
          </p>
        </div>

        <DetailedAnalytics />
      </div>
    </div>
  );
};

export default MarketAnalytics;
