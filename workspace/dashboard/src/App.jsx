import React, { useState } from 'react';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import PropertyMap from './components/PropertyMap';
import MarketAnalytics from './components/MarketAnalytics';
import PropertyListings from './components/PropertyListings';

function App() {
  const [activeView, setActiveView] = useState('dashboard');

  const renderActiveView = () => {
    switch(activeView) {
      case 'dashboard':
        return <Dashboard />;
      case 'listings':
        return <PropertyListings />;
      case 'properties':
        return <PropertyMap />;
      case 'analytics':
        return <MarketAnalytics />;
      case 'saved':
        return (
          <div className="glass p-8 rounded-2xl shadow-xl border border-white/20 animate-slide-in">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Saved Properties</h2>
                <p className="text-gray-600 font-medium">Your favorite properties and watchlist</p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-xl border border-gray-200/50">
              <p className="text-gray-600 text-center">Your saved properties will appear here.</p>
            </div>
          </div>
        );
      case 'agents':
        return (
          <div className="glass p-8 rounded-2xl shadow-xl border border-white/20 animate-slide-in">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Agent Directory</h2>
                <p className="text-gray-600 font-medium">Connect with real estate professionals</p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-xl border border-gray-200/50">
              <p className="text-gray-600 text-center">Agent directory and contact information coming soon.</p>
            </div>
          </div>
        );
      case 'reports':
        return (
          <div className="glass p-8 rounded-2xl shadow-xl border border-white/20 animate-slide-in">
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">Market Reports</h2>
                <p className="text-gray-600 font-medium">Comprehensive market analysis and insights</p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-gray-50 to-white p-6 rounded-xl border border-gray-200/50">
              <p className="text-gray-600 text-center">Market analysis reports coming soon.</p>
            </div>
          </div>
        );
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100">
      <Header />
      <div className="flex flex-1">
        <Sidebar activeView={activeView} onViewChange={setActiveView} />
        <main className="flex-1 p-8 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {renderActiveView()}
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;