import React, { useState } from 'react';
import { propertyListings } from '../data/realEstateData';

const PropertyListings = () => {
  const [sortBy, setSortBy] = useState('pricePerSqft');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Filter and sort properties
  const getFilteredProperties = () => {
    let filtered = propertyListings;

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(prop => prop.propertyType.toLowerCase() === filterType);
    }

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(prop => prop.status.toLowerCase() === filterStatus.toLowerCase());
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(prop => 
        prop.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prop.propertyType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        prop.owner.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sort properties
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'pricePerSqft':
          return b.pricePerSqft - a.pricePerSqft;
        case 'sqft':
          return b.sqft - a.sqft;
        case 'monthlyRent':
          return b.monthlyRent - a.monthlyRent;
        case 'yearBuilt':
          return b.yearBuilt - a.yearBuilt;
        default:
          return 0;
      }
    });

    return filtered;
  };

  const filteredProperties = getFilteredProperties();

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'leased':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPropertyTypeColor = (type) => {
    switch (type.toLowerCase()) {
      case 'retail':
        return 'bg-blue-100 text-blue-800';
      case 'office':
        return 'bg-purple-100 text-purple-800';
      case 'restaurant':
        return 'bg-orange-100 text-orange-800';
      case 'warehouse':
        return 'bg-gray-100 text-gray-800';
      case 'mixed use':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <div className="glass p-6 rounded-2xl shadow-xl border border-white/20">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
          <div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2">
              Available Properties
            </h2>
            <p className="text-gray-600 font-medium">
              {filteredProperties.length} properties found
            </p>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">Search</label>
            <input
              type="text"
              placeholder="Search by address, type, or owner..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 bg-white/80 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            />
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">Property Type</label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full px-4 py-2 bg-white/80 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            >
              <option value="all">All Types</option>
              <option value="retail">Retail</option>
              <option value="office">Office</option>
              <option value="restaurant">Restaurant</option>
              <option value="warehouse">Warehouse</option>
              <option value="mixed use">Mixed Use</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-4 py-2 bg-white/80 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            >
              <option value="all">All Status</option>
              <option value="available">Available</option>
              <option value="leased">Leased</option>
              <option value="pending">Pending</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-2">Sort By</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="w-full px-4 py-2 bg-white/80 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            >
              <option value="pricePerSqft">Price per SqFt</option>
              <option value="monthlyRent">Monthly Rent</option>
              <option value="sqft">Square Footage</option>
              <option value="yearBuilt">Year Built</option>
            </select>
          </div>
        </div>
      </div>

      {/* Property Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredProperties.map((property) => (
          <div key={property.id} className="glass p-6 rounded-2xl shadow-xl border border-white/20 hover:shadow-2xl hover:scale-105 transition-all duration-300 group">
            {/* Property Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors">
                  {property.address}
                </h3>
                <div className="flex items-center space-x-2 mb-2">
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getPropertyTypeColor(property.propertyType)}`}>
                    {property.propertyType}
                  </span>
                  <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(property.status)}`}>
                    {property.status}
                  </span>
                </div>
              </div>
            </div>

            {/* Property Details */}
            <div className="space-y-3 mb-6">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 font-medium">Monthly Rent:</span>
                <span className="text-2xl font-bold text-green-600">${property.monthlyRent.toLocaleString()}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600 font-medium">Price per SqFt:</span>
                <span className="text-lg font-semibold text-blue-600">${property.pricePerSqft}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600 font-medium">Square Footage:</span>
                <span className="text-lg font-semibold text-gray-800">{property.sqft.toLocaleString()} sqft</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600 font-medium">Year Built:</span>
                <span className="text-lg font-semibold text-gray-800">{property.yearBuilt}</span>
              </div>
            </div>

            {/* Property Description */}
            <p className="text-gray-600 text-sm mb-4 line-clamp-2">
              {property.description}
            </p>

            {/* Amenities */}
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">Amenities:</h4>
              <div className="flex flex-wrap gap-1">
                {property.amenities.slice(0, 3).map((amenity, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-lg">
                    {amenity}
                  </span>
                ))}
                {property.amenities.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-lg">
                    +{property.amenities.length - 3} more
                  </span>
                )}
              </div>
            </div>

            {/* Agent Info */}
            <div className="border-t border-gray-200 pt-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-800">{property.agent.name}</p>
                  <p className="text-xs text-gray-600">{property.agent.phone}</p>
                </div>
                <div className="flex space-x-2">
                  <button className="px-4 py-2 bg-blue-600 text-white text-sm font-semibold rounded-lg hover:bg-blue-700 transition-colors">
                    Contact
                  </button>
                  <button className="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-semibold rounded-lg hover:bg-gray-200 transition-colors">
                    Save
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* No Results */}
      {filteredProperties.length === 0 && (
        <div className="glass p-12 rounded-2xl shadow-xl border border-white/20 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">No Properties Found</h3>
          <p className="text-gray-600">Try adjusting your filters or search terms to find more properties.</p>
        </div>
      )}
    </div>
  );
};

export default PropertyListings;
