import React, { useEffect, useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';
import { propertyListings } from '../data/realEstateData';
import { findNearbyProperties } from '../utils/locationUtils';

const GoogleMap = ({ selectedProperty, onPropertySelect, onNearbyPropertiesFound, onMapError }) => {
  const mapRef = useRef(null);
  const searchInputRef = useRef(null);
  const [map, setMap] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [infoWindow, setInfoWindow] = useState(null);
  const [autocomplete, setAutocomplete] = useState(null);
  const [searchMarker, setSearchMarker] = useState(null);
  const [searchLocation, setSearchLocation] = useState(null);
  const [mapError, setMapError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Only initialize if we don't already have a map
    if (map) return;

    const initializeMap = () => {
      // Add global error handler for Google Maps
      window.gm_authFailure = () => {
        console.error('Google Maps authentication failed');
        setMapError('Google Maps API authentication failed. Please check the API key.');
        setIsLoading(false);
        if (onMapError) {
          onMapError(new Error('Authentication failed'));
        }
      };

      const loader = new Loader({
        apiKey: "AIzaSyDT_5w6Tl13cF3hQ3DINP60F5CVOSQ1pGs",
        version: "weekly",
        libraries: ["places"],
        region: "US",
        language: "en"
      });

      loader.load().then(() => {
        // Ensure the map container exists
        if (!mapRef.current) {
          console.error('Map container not found');
          setMapError('Map container not available');
          setIsLoading(false);
          return;
        }

        const mapInstance = new window.google.maps.Map(mapRef.current, {
        center: { lat: 40.7580, lng: -73.9855 }, // NYC center
        zoom: 13,
        styles: [
          {
            featureType: "poi.business",
            stylers: [{ visibility: "off" }]
          }
        ]
      });

      const infoWindowInstance = new window.google.maps.InfoWindow();

      // Initialize autocomplete
      if (searchInputRef.current) {
        const autocompleteInstance = new window.google.maps.places.Autocomplete(
          searchInputRef.current,
          {
            types: ['establishment', 'geocode'],
            componentRestrictions: { country: 'us' }
          }
        );

        autocompleteInstance.addListener('place_changed', () => {
          const place = autocompleteInstance.getPlace();

          if (!place.geometry || !place.geometry.location) {
            console.log("No details available for input: '" + place.name + "'");
            return;
          }

          // Clear existing search marker
          if (searchMarker) {
            searchMarker.setMap(null);
          }

          // Create new search marker
          const newSearchMarker = new window.google.maps.Marker({
            position: place.geometry.location,
            map: mapInstance,
            title: place.name,
            icon: {
              url: 'data:image/svg+xml;charset=UTF-8,%3Csvg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="%23dc2626"%3E%3Cpath d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/%3E%3C/svg%3E',
              scaledSize: new window.google.maps.Size(40, 40)
            }
          });

          setSearchMarker(newSearchMarker);

          // Store search location
          const location = {
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
            name: place.name,
            address: place.formatted_address
          };
          setSearchLocation(location);

          // Pan to the location
          mapInstance.panTo(place.geometry.location);
          mapInstance.setZoom(15);

          // Find nearby properties
          const nearbyProperties = findNearbyProperties(
            propertyListings,
            location.lat,
            location.lng,
            2 // 2 mile radius
          );

          // Notify parent component about nearby properties
          if (onNearbyPropertiesFound) {
            onNearbyPropertiesFound(nearbyProperties, location);
          }

          // Show info window for searched location
          const searchInfoContent = `
            <div style="max-width: 250px;">
              <h3 style="margin: 0 0 8px 0; font-weight: 600;">${place.name}</h3>
              <p style="margin: 0; color: #6b7280; font-size: 14px;">${place.formatted_address}</p>
              ${place.rating ? `<p style="margin: 4px 0 0 0; font-size: 12px;">⭐ ${place.rating} rating</p>` : ''}
              <p style="margin: 8px 0 0 0; font-size: 12px; color: #16a34a;">
                ${nearbyProperties.length} properties within 2 miles
              </p>
            </div>
          `;

          infoWindowInstance.setContent(searchInfoContent);
          infoWindowInstance.open(mapInstance, newSearchMarker);
        });

        setAutocomplete(autocompleteInstance);
      }

      setMap(mapInstance);
      setInfoWindow(infoWindowInstance);
    }).catch((error) => {
      console.error('Google Maps loading error:', error);
      setMapError(error.message || 'Failed to load Google Maps');
      setIsLoading(false);
      if (onMapError) {
        onMapError(error);
      }
    });
    };

    // Initialize immediately since we're checking for map existence
    initializeMap();
  }, []); // Empty dependency array to run only once

  useEffect(() => {
    if (!map || !window.google) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    // Create new markers for properties
    const newMarkers = propertyListings.map(property => {
      const marker = new window.google.maps.Marker({
        position: { lat: property.latitude, lng: property.longitude },
        map: map,
        title: property.address,
        icon: {
          url: getMarkerIcon(property.propertyType),
          scaledSize: new window.google.maps.Size(40, 40)
        }
      });

      marker.addListener('click', () => {
        // Create content div programmatically
        const contentDiv = document.createElement('div');
        contentDiv.style.maxWidth = '300px';
        
        contentDiv.innerHTML = `
          <h3 style="margin: 0 0 8px 0; font-weight: 600;">${property.address}</h3>
          <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span style="background: #dbeafe; color: #1e40af; padding: 2px 8px; border-radius: 4px; font-size: 12px;">
              ${property.propertyType}
            </span>
            <span style="color: #16a34a; font-weight: 600;">
              $${property.monthlyRent.toLocaleString()}/mo
            </span>
          </div>
          <p style="margin: 8px 0; color: #6b7280; font-size: 14px;">
            ${property.sqft.toLocaleString()} sqft • $${property.pricePerSqft}/sqft
          </p>
          <p style="margin: 8px 0; font-size: 14px;">${property.description}</p>
          <div style="margin-top: 12px; padding-top: 8px; border-top: 1px solid #e5e7eb;">
            <p style="margin: 0; font-size: 12px; color: #6b7280;">
              Agent: ${property.agent.name}<br>
              ${property.agent.phone}
            </p>
          </div>
        `;
        
        // Create button separately and add event listener
        const button = document.createElement('button');
        button.textContent = 'View Details';
        button.style.cssText = `
          margin-top: 8px; 
          background: #2563eb; 
          color: white; 
          padding: 6px 12px; 
          border: none; 
          border-radius: 4px; 
          cursor: pointer;
          font-size: 12px;
        `;
        
        button.addEventListener('click', () => {
          if (onPropertySelect) {
            onPropertySelect(property);
          }
          infoWindow.close();
        });
        
        contentDiv.appendChild(button);
        infoWindow.setContent(contentDiv);
        infoWindow.open(map, marker);
      });

      return marker;
    });

    setMarkers(newMarkers);
  }, [map, infoWindow, onPropertySelect]);

  // Center map on selected property
  useEffect(() => {
    if (map && selectedProperty) {
      map.panTo({ 
        lat: selectedProperty.latitude, 
        lng: selectedProperty.longitude 
      });
      map.setZoom(15);
    }
  }, [map, selectedProperty]);

  const getMarkerIcon = (propertyType) => {
    const iconColors = {
      'Retail': '#3b82f6',      // blue
      'Office': '#10b981',      // green
      'Restaurant': '#f59e0b',  // orange
      'Warehouse': '#8b5cf6',   // purple
      'Mixed Use': '#ef4444'    // red
    };
    
    const color = iconColors[propertyType] || '#6b7280';
    
    return `data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 24 24' fill='${encodeURIComponent(color)}'%3E%3Cpath d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'/%3E%3C/svg%3E`;
  };

  // Show error state
  if (mapError) {
    return (
      <div className="w-full h-64 rounded-lg border-2 border-gray-200 flex items-center justify-center bg-gray-50" style={{ minHeight: '400px' }}>
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">Map Unavailable</h3>
          <p className="text-gray-600 mb-4 max-w-md">
            Unable to load Google Maps. This might be due to API key restrictions or network issues.
          </p>
          <p className="text-sm text-gray-500">
            Error: {mapError}
          </p>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="w-full h-64 rounded-lg border-2 border-gray-200 flex items-center justify-center bg-gray-50" style={{ minHeight: '400px' }}>
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading map...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Search Box */}
      <div className="absolute top-4 left-4 z-10 w-80">
        <div className="relative">
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search for places, addresses, or businesses..."
            className="w-full px-4 py-3 pr-12 bg-white border border-gray-300 rounded-lg shadow-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
            disabled={!map}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        {/* Search Actions */}
        <div className="absolute top-full mt-2 flex space-x-2">
          <button
            onClick={() => {
              if (searchInputRef.current) {
                searchInputRef.current.value = '';
              }
              if (searchMarker) {
                searchMarker.setMap(null);
                setSearchMarker(null);
              }
              if (infoWindow) {
                infoWindow.close();
              }
              setSearchLocation(null);
              if (onNearbyPropertiesFound) {
                onNearbyPropertiesFound([], null);
              }
            }}
            className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-600 text-xs rounded transition-colors"
          >
            Clear Search
          </button>

          {searchLocation && (
            <button
              onClick={() => {
                const nearbyProperties = findNearbyProperties(
                  propertyListings,
                  searchLocation.lat,
                  searchLocation.lng,
                  5 // Expand to 5 mile radius
                );
                if (onNearbyPropertiesFound) {
                  onNearbyPropertiesFound(nearbyProperties, searchLocation);
                }
              }}
              className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-600 text-xs rounded transition-colors"
            >
              Expand Search (5mi)
            </button>
          )}
        </div>
      </div>

      {/* Map Container - Always render to ensure ref is available */}
      <div
        ref={mapRef}
        className="w-full h-64 rounded-lg border-2 border-gray-200"
        style={{ minHeight: '400px' }}
      />
    </div>
  );
};

export default GoogleMap;