// Commercial Real Estate Mock Data

export const propertyListings = [
  {
    id: 1,
    address: "123 Main St, Downtown",
    propertyType: "Retail",
    sqft: 2500,
    monthlyRent: 8500,
    pricePerSqft: 3.40,
    latitude: 40.7589,
    longitude: -73.9851,
    status: "Available",
    owner: "Downtown Properties LLC",
    agent: {
      name: "<PERSON>",
      phone: "(*************",
      email: "<EMAIL>"
    },
    amenities: ["Street Parking", "High Foot Traffic", "Corner Location"],
    zoning: "C-1 Commercial",
    yearBuilt: 1985,
    description: "Prime retail space in the heart of downtown with excellent visibility."
  },
  {
    id: 2,
    address: "456 Business Ave, Financial District",
    propertyType: "Office",
    sqft: 5000,
    monthlyRent: 15000,
    pricePerSqft: 3.00,
    latitude: 40.7505,
    longitude: -73.9934,
    status: "Available",
    owner: "Metro Commercial Group",
    agent: {
      name: "<PERSON>",
      phone: "(*************",
      email: "<EMAIL>"
    },
    amenities: ["Parking Garage", "Conference Rooms", "Elevator Access"],
    zoning: "C-2 Commercial",
    yearBuilt: 1998,
    description: "Modern office space perfect for financial services or consulting firms."
  },
  {
    id: 3,
    address: "789 Restaurant Row, Arts District",
    propertyType: "Restaurant",
    sqft: 3200,
    monthlyRent: 12000,
    pricePerSqft: 3.75,
    latitude: 40.7614,
    longitude: -73.9776,
    status: "Available",
    owner: "Arts District Holdings",
    agent: {
      name: "Lisa Rodriguez",
      phone: "(*************",
      email: "<EMAIL>"
    },
    amenities: ["Full Kitchen", "Liquor License", "Outdoor Seating"],
    zoning: "C-1 Commercial",
    yearBuilt: 1992,
    description: "Turn-key restaurant space with full commercial kitchen and bar setup."
  }
];

export const marketStats = [
  {
    title: 'Available Properties',
    value: '147',
    change: '****%',
    trend: 'up',
    icon: {
      path: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
      bgColor: 'bg-blue-500'
    }
  },
  {
    title: 'Avg. Rent/SqFt',
    value: '$3.25',
    change: '****%',
    trend: 'up',
    icon: {
      path: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
      bgColor: 'bg-green-500'
    }
  },
  {
    title: 'Vacancy Rate',
    value: '12.3%',
    change: '-1.5%',
    trend: 'down',
    icon: {
      path: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      bgColor: 'bg-yellow-500'
    }
  },
  {
    title: 'New Listings',
    value: '23',
    change: '+15.3%',
    trend: 'up',
    icon: {
      path: 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6',
      bgColor: 'bg-purple-500'
    }
  }
];

export const propertyTypeData = [
  { name: 'Retail', value: 45, color: '#0088FE' },
  { name: 'Office', value: 35, color: '#00C49F' },
  { name: 'Restaurant', value: 25, color: '#FFBB28' },
  { name: 'Warehouse', value: 15, color: '#FF8042' },
  { name: 'Mixed Use', value: 12, color: '#8884d8' }
];

export const monthlyRentTrends = [
  { month: 'Jan', retail: 3200, office: 2800, restaurant: 3500 },
  { month: 'Feb', retail: 3250, office: 2850, restaurant: 3600 },
  { month: 'Mar', retail: 3300, office: 2900, restaurant: 3650 },
  { month: 'Apr', retail: 3400, office: 2950, restaurant: 3700 },
  { month: 'May', retail: 3450, office: 3000, restaurant: 3750 },
  { month: 'Jun', retail: 3500, office: 3100, restaurant: 3800 }
];

export const zipCodeAnalytics = [
  { zipCode: '10001', avgRent: 3.50, vacancyRate: 8.2, properties: 45 },
  { zipCode: '10002', avgRent: 3.25, vacancyRate: 12.1, properties: 38 },
  { zipCode: '10003', avgRent: 3.75, vacancyRate: 6.5, properties: 52 },
  { zipCode: '10004', avgRent: 4.00, vacancyRate: 5.8, properties: 29 },
  { zipCode: '10005', avgRent: 3.10, vacancyRate: 15.2, properties: 33 }
];

export const footTrafficData = [
  { hour: '9AM', weekday: 120, weekend: 80 },
  { hour: '12PM', weekday: 300, weekend: 250 },
  { hour: '3PM', weekday: 200, weekend: 180 },
  { hour: '6PM', weekday: 400, weekend: 350 },
  { hour: '9PM', weekday: 180, weekend: 420 }
];

// Additional market analytics data
export const marketTrends = {
  quarterlyGrowth: [
    { quarter: 'Q1 2024', growth: 2.3, avgRent: 3.15 },
    { quarter: 'Q2 2024', growth: 3.1, avgRent: 3.25 },
    { quarter: 'Q3 2024', growth: 2.8, avgRent: 3.34 },
    { quarter: 'Q4 2024', growth: 3.5, avgRent: 3.46 }
  ],
  occupancyRates: {
    retail: 87.7,
    office: 82.3,
    restaurant: 91.2,
    warehouse: 94.5,
    mixedUse: 85.8
  },
  priceRanges: {
    retail: { min: 2.50, max: 4.20, avg: 3.25 },
    office: { min: 2.00, max: 3.80, avg: 2.85 },
    restaurant: { min: 3.00, max: 4.50, avg: 3.75 },
    warehouse: { min: 1.50, max: 2.80, avg: 2.15 },
    mixedUse: { min: 2.80, max: 4.00, avg: 3.40 }
  }
};

export const competitiveAnalysis = [
  { competitor: 'Downtown Properties', marketShare: 23.5, avgRent: 3.45, properties: 45 },
  { competitor: 'Metro Commercial', marketShare: 18.2, avgRent: 3.20, properties: 38 },
  { competitor: 'Prime Locations LLC', marketShare: 15.8, avgRent: 3.65, properties: 32 },
  { competitor: 'Urban Spaces', marketShare: 12.4, avgRent: 3.10, properties: 28 },
  { competitor: 'Others', marketShare: 30.1, avgRent: 3.25, properties: 67 }
];